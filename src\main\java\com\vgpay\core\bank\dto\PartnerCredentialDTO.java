package com.vgpay.core.bank.dto;

import com.vgpay.core.bank.model.AuthType;
import com.vgpay.core.bank.model.CredentialStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PartnerCredentialDTO {

    Long id;
    Long partnerId;
    AuthType authType;
    String apiKey;
    String clientId;
    String clientSecret;
    String certificate;
    Integer version;
    CredentialStatus status;
    Instant createdAt;
    Instant updatedAt;

}
