package com.vgpay.core.device.model;

import jakarta.persistence.*;
import lombok.*;

import java.time.Instant;

@Entity
@Table(name = "device_status_log")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DeviceStatusLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;   // PK

    @Column(name = "device_id", nullable = false)
    private Long deviceId;   // FK → Device.id

    @Column(name = "battery_level")
    private String batteryLevel;   // Example: "80%"

    @Column(name = "network_strength")
    private String networkStrength;   // Example: "Good", "Weak", "-85dBm"

    @Column(name = "firmware_version")
    private String firmwareVersion;   // Device firmware version

    @Column(name = "timestamp", nullable = false)
    private Instant timestamp;   // Time log was recorded


}
