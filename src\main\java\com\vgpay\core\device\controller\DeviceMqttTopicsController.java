package com.vgpay.core.device.controller;

import com.vgpay.core.device.dto.DeviceMqttTopicsDTO;
import com.vgpay.core.device.service.DeviceMqttTopicsService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/device-mqtt-topics")
public class DeviceMqttTopicsController {

    private final DeviceMqttTopicsService deviceMqttTopicsService;

    public DeviceMqttTopicsController(DeviceMqttTopicsService deviceMqttTopicsService) {
        this.deviceMqttTopicsService = deviceMqttTopicsService;
    }

    @PostMapping
    public ResponseEntity<DeviceMqttTopicsDTO> create(@RequestBody DeviceMqttTopicsDTO dto) {
        return ResponseEntity.ok(deviceMqttTopicsService.createDeviceMqttTopics(dto));
    }

    @PutMapping("/{id}")
    public ResponseEntity<DeviceMqttTopicsDTO> update(@PathVariable Long id, @RequestBody DeviceMqttTopicsDTO dto) {
        return ResponseEntity.ok(deviceMqttTopicsService.updateDeviceMqttTopics(id, dto));
    }

    @GetMapping("/{id}")
    public ResponseEntity<DeviceMqttTopicsDTO> getById(@PathVariable Long id) {
        return ResponseEntity.ok(deviceMqttTopicsService.getDeviceMqttTopics(id));
    }

    @GetMapping("/all")
    public ResponseEntity<List<DeviceMqttTopicsDTO>> getByDeviceId() {
        return ResponseEntity.ok(deviceMqttTopicsService.getAllDeviceMqttTopics());
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<String> delete(@PathVariable Long id) {
        deviceMqttTopicsService.deleteDeviceMqttTopics(id);
        return ResponseEntity.ok("DeviceMqttTopics deleted successfully");
    }

}
