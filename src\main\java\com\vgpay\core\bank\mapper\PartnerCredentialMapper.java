package com.vgpay.core.bank.mapper;

import com.vgpay.core.bank.dto.BankPartnerDTO;
import com.vgpay.core.bank.dto.PartnerCredentialDTO;
import com.vgpay.core.bank.model.BankPartner;
import com.vgpay.core.bank.model.PartnerCredential;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring")
public interface PartnerCredentialMapper {
    PartnerCredentialDTO toDto(PartnerCredential entity);
    PartnerCredential toEntity(PartnerCredentialDTO dto);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void updatePartnerCredentialFromDto(PartnerCredentialDTO dto, @MappingTarget PartnerCredential entity);

}
