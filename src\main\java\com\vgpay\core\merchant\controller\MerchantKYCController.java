package com.vgpay.core.merchant.controller;

import com.vgpay.core.merchant.dto.MerchantKycDTO;
import com.vgpay.core.merchant.service.MerchantKycService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/merchant-kyc")
public class MerchantKYCController {

    private final MerchantKycService merchantKycService;


    public MerchantKYCController(MerchantKycService merchantKycService) {
        this.merchantKycService = merchantKycService;
    }

    @PostMapping
    public ResponseEntity<MerchantKycDTO> createMerchantKyc(@RequestBody MerchantKycDTO dto) {
        return ResponseEntity.ok(merchantKycService.createMerchantKyc(dto));
    }

    @GetMapping
    public ResponseEntity<List<MerchantKycDTO>> getAllMerchantKyc() {
        return ResponseEntity.ok(merchantKycService.getAllMerchantKyc());
    }

    @GetMapping("/{id}")
    public ResponseEntity<MerchantKycDTO> getMerchantKycById(@PathVariable Long id) {
        return ResponseEntity.ok(merchantKycService.getMerchantKycById(id));
    }

    @PutMapping("/{id}")
    public ResponseEntity<MerchantKycDTO> updateMerchantKyc(@PathVariable Long id, @RequestBody MerchantKycDTO dto) {
        return ResponseEntity.ok(merchantKycService.updateMerchantKyc(id, dto));
    }

//    @DeleteMapping
//    public void deleteMerchantKyc(@PathVariable Long id) {
//        merchantKycService.deleteMerchantKyc(id);
//    }


}
