package com.vgpay.core.bank.mapper;

import com.vgpay.core.bank.dto.BankPartnerDTO;
import com.vgpay.core.bank.model.BankPartner;
import com.vgpay.core.device.dto.DeviceDTO;
import com.vgpay.core.device.model.Device;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring")
public interface BankPartnerMapper {

    BankPartnerDTO toDto(BankPartner entity);
    BankPartner toEntity(BankPartnerDTO dto);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void updateBankPartnerFromDto(BankPartnerDTO dto, @MappingTarget BankPartner entity);
}
