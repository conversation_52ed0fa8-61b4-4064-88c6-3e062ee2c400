# Docker ignore file for VG Pay Backend

# Target directory (Maven build output)
target/
!target/vgpay-core-*.jar

# IDE files
.idea/
.vscode/
*.iml
*.ipr
*.iws

# OS files
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Docker files (to avoid copying them into the image)
Dockerfile
docker-compose.yml
.dockerignore

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp

# Maven wrapper jar (we copy it explicitly)
.mvn/wrapper/maven-wrapper.jar

# Node modules (if any)
node_modules/

# Environment files
.env
.env.local
.env.*.local

# Documentation
README.md
docs/

# Test coverage reports
coverage/
*.coverage

# Backup files
*.bak
*.backup
