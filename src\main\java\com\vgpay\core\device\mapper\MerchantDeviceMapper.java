package com.vgpay.core.device.mapper;

import com.vgpay.core.device.dto.DeviceDTO;
import com.vgpay.core.device.dto.MerchantDeviceDTO;
import com.vgpay.core.device.model.Device;
import com.vgpay.core.device.model.MerchantDevice;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;


@Mapper(componentModel = "spring")
public interface MerchantDeviceMapper {

    MerchantDeviceDTO toDto(MerchantDevice merchantDevice);
    MerchantDevice toEntity(MerchantDeviceDTO merchantDeviceDTO);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void updateMerchantDeviceFromDto(MerchantDeviceDTO merchantDeviceDTO, @MappingTarget MerchantDevice entity);
}
