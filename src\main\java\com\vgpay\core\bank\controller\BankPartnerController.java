package com.vgpay.core.bank.controller;

import com.vgpay.core.bank.dto.BankPartnerDTO;
import com.vgpay.core.bank.service.BankPartnerService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/partners")
public class BankPartnerController {

    private final BankPartnerService bankPartnerService;

    public BankPartnerController(BankPartnerService bankPartnerService) {
        this.bankPartnerService = bankPartnerService;
    }

    @PostMapping
    public ResponseEntity<BankPartnerDTO>  createBankPartner(@RequestBody BankPartnerDTO dto) {
        return ResponseEntity.ok(bankPartnerService.createBankPartner(dto));
    }

    @PutMapping("/{id}")
    public ResponseEntity<BankPartnerDTO>  updateBankPartner(@PathVariable Long id, @RequestBody BankPartnerDTO dto) {
        return ResponseEntity.ok(bankPartnerService.updateBankPartner(id, dto));
    }

    @GetMapping("/{id}")
    public ResponseEntity<BankPartnerDTO> getBankPartner(@PathVariable Long id) {
        return ResponseEntity.ok(bankPartnerService.getBankPartner(id));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<String> deleteBankPartner(@PathVariable Long id) {
        bankPartnerService.deleteBankPartner(id);
        return ResponseEntity.ok("BankPartner deleted successfully");
    }

    @GetMapping("/all")
    public ResponseEntity<List<BankPartnerDTO>> getAllBankPartners() {
        return ResponseEntity.ok(bankPartnerService.getAllActiveBankPartners());
    }

}
