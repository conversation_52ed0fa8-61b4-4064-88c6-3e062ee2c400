package com.vgpay.core.device.controller;

import com.vgpay.core.device.dto.MerchantDeviceDTO;
import com.vgpay.core.device.service.MerchantDeviceService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/merchant-devices")
public class MerchantDeviceController {

    private final MerchantDeviceService service;

    public MerchantDeviceController(MerchantDeviceService service) {
        this.service = service;
    }

    @PostMapping
    public ResponseEntity<MerchantDeviceDTO> assignDevice(@RequestBody MerchantDeviceDTO dto) {
        return ResponseEntity.ok(service.assignDevice(dto));
    }

    @GetMapping("/{id}")
    public  ResponseEntity<MerchantDeviceDTO> getById(@PathVariable Long id) {
        return ResponseEntity.ok(service.getById(id));
    }

    @GetMapping("/merchant/{merchantId}")
    public ResponseEntity<List<MerchantDeviceDTO>> getByMerchant(@PathVariable Long merchantId) {
        return ResponseEntity.ok(service.getMerchantDeviceByMerchantId(merchantId));
    }

    @GetMapping("/device/{deviceId}")
    public ResponseEntity<List<MerchantDeviceDTO>> getByDevice(@PathVariable Long deviceId) {
        return ResponseEntity.ok(service.getByDeviceId(deviceId));
    }

    @PutMapping("/{id}/status")
    public ResponseEntity<MerchantDeviceDTO> updateStatus(@PathVariable Long id, @RequestBody MerchantDeviceDTO dto) {
        return ResponseEntity.ok(service.updateMerchantDevice(id, dto));
    }

}
