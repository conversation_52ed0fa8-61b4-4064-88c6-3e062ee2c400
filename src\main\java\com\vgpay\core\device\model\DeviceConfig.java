package com.vgpay.core.device.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.Instant;

@Entity
@Table(name = "device_config")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class DeviceConfig {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;   // PK

    @Column(name = "device_id", nullable = false)
    private Long deviceId;   // FK → Device.id

    @Column(name = "audio_lang")
    private String audioLang;

    @Column(name = "mqtt_broker")
    private String mqttBroker;

    @Column(name = "heartbeat_interval")
    private Integer heartbeatInterval;

    @Column(name = "retained_config")
    private Boolean retainedConfig;

    @Column(name = "last_modified")
    private Instant lastModified;

}
