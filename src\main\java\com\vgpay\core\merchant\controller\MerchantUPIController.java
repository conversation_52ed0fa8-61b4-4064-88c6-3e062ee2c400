package com.vgpay.core.merchant.controller;

import com.vgpay.core.merchant.dto.MerchantUpiDTO;
import com.vgpay.core.merchant.service.MerchantUPIService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/merchant-upis")
public class MerchantUPIController {

    private final MerchantUPIService merchantUPIService;

    public MerchantUPIController(MerchantUPIService merchantUPIService) {
        this.merchantUPIService = merchantUPIService;
    }

    @PostMapping
    public ResponseEntity<MerchantUpiDTO> createMerchantUPI(@RequestBody MerchantUpiDTO merchantUpiDTO) {
        return ResponseEntity.ok(merchantUPIService.createMerchantUPI(merchantUpiDTO));
    }

    @PutMapping("/{id}")
    public ResponseEntity<MerchantUpiDTO> updateMerchantUPI(@PathVariable Long id, @RequestBody MerchantUpiDTO merchantUpiDTO) {
        return ResponseEntity.ok(merchantUPIService.updateMerchantUPI(id, merchantUpiDTO));
    }

    @GetMapping("/{id}")
    public ResponseEntity<MerchantUpiDTO> getMerchantUPIById(@PathVariable Long id) {
        return ResponseEntity.ok(merchantUPIService.getMerchantUPIById(id));
    }

    @DeleteMapping("/{id}")
    public void deleteMerchantUPI(@PathVariable Long id) {
        merchantUPIService.deleteMerchantUPI(id);
    }

    @GetMapping
    public List<MerchantUpiDTO> getAllMerchantUPIs() {
        return merchantUPIService.getAllMerchantUPIs();
    }

}
