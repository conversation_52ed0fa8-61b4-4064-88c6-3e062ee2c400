package com.vgpay.core.transaction.dto;

import com.vgpay.core.transaction.model.TransactionStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TransactionDTO {
    private Long id;
    private String transactionId;
    private String utr;
    private Long merchantId;
    private Long deviceId;
    private Double amount;
    private String currency;
    private TransactionStatus status;
    private String payerVpa;
    private String payerName;
    private String gatewayResponse;
    private String acknowledgedAt;
    private String remarks;
    private Instant createdAt;
}
