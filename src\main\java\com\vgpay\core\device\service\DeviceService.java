package com.vgpay.core.device.service;

import com.vgpay.core.common.MqttTopicResolver;
import com.vgpay.core.device.dto.*;
import com.vgpay.core.device.mapper.DeviceMapper;
import com.vgpay.core.device.model.Device;
import com.vgpay.core.device.model.DeviceConfig;
import com.vgpay.core.device.model.DeviceStatus;
import com.vgpay.core.device.repository.DeviceConfigRepository;
import com.vgpay.core.device.repository.DeviceRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.server.ResponseStatusException;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
public class DeviceService {

    @Autowired
    private DeviceConfigRepository configRepository;

    @Autowired
    private MqttTopicResolver mqttTopicResolver;

    private final DeviceRepository deviceRepository;
    private final DeviceMapper deviceMapper;

    public DeviceService(DeviceRepository deviceRepository, DeviceMapper deviceMapper) {
        this.deviceRepository = deviceRepository;
        this.deviceMapper = deviceMapper;
    }

    public DeviceDTO createDevice(DeviceDTO dto) {
        Device device = deviceMapper.toEntity(dto);
        Device savedDevice = deviceRepository.save(device);
        return deviceMapper.toDto(savedDevice);
    }

    public DeviceDTO getDevice(Long id) {
        Device device = deviceRepository.findById(id).orElse(null);
        return deviceMapper.toDto(device);
    }

    public DeviceDTO getDeviceByCode(String code) {
        Device device = deviceRepository.findByCode(code).orElse(null);
        return deviceMapper.toDto(device);
    }

    public DeviceDTO getDeviceBySerialNumber(String serialNumber) {
        Device device = deviceRepository.findBySerialNumber(serialNumber).orElse(null);
        return deviceMapper.toDto(device);
    }

    public DeviceDTO getDeviceByImei(String imei) {
        Device device = deviceRepository.findByImei(imei).orElse(null);
        return deviceMapper.toDto(device);
    }

    public List<DeviceDTO> getAllDevice() {
        return deviceRepository.findAll()
                .stream()
                .filter(device -> device.getStatus() == DeviceStatus.ACTIVE)
                .map(deviceMapper::toDto)
                .collect(Collectors.toList());
    }

    public DeviceDTO updateDevice(Long id, DeviceDTO dto) {
        Device device = deviceRepository.findById(id).
                orElseThrow(() -> new RuntimeException("Device not found"));
        deviceMapper.updateDeviceFromDto(dto, device);
        Device updatedDevice = deviceRepository.save(device);
        return deviceMapper.toDto(updatedDevice);

    }

    public void deleteDevice(Long id) {
        Device device = deviceRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, " Device Not found "));
        if (device.getStatus() != DeviceStatus.INACTIVE) {
            device.setStatus(DeviceStatus.INACTIVE);
            deviceRepository.save(device);
        }
    }


    /**
     * Device Registration Flow
     */
    public DeviceRegisterResponse verifyDevice(DeviceRegisterRequest request) {
        System.out.println("request.getDeviceImei()----" + request.getDeviceImei());
        Optional<Device> deviceOpt = deviceRepository.findByImei(request.getDeviceImei());
        System.out.println("deviceOpt-- "+ deviceOpt);

        if (deviceOpt.isEmpty()) {
            // Not found → return failure response directly
            return new DeviceRegisterResponse(
                    false,
                    404,
                    "Device with given serial/IMEI not found in system",
                    null,
                    null,
                    Instant.now().toString()
            );
        }

        Device device = deviceOpt.get();

        // Found → return success response
        return new DeviceRegisterResponse(
                true,
                200,
                "Device already registered in system",
                device.getSerialNumber(),
                UUID.randomUUID().toString().replace("-", ""), // auth token
                Instant.now().toString()
        );
    }

    /**
     * Device Handshake → fetch config
     */
    public DeviceConfigResponse fetchConfig(DeviceConfigRequest request) {
        Device device = deviceRepository.findBySerialNumber(request.getSerialNumber())
                .orElseThrow(() -> new RuntimeException("Device not found"));

        DeviceConfig deviceConfig = configRepository.findByDeviceId(device.getId())
                .orElseThrow(() -> new RuntimeException("DeviceConfig not found"));

        // Dynamically resolve topics for this device
        Map<String, Map<String, String>> topics = mqttTopicResolver.resolveTopics(device.getSerialNumber());

        MqttConfigDTO mqttConfig = new MqttConfigDTO(
//                "40.192.88.102", // broker_url
                deviceConfig.getMqttBroker(),
                8083,            // port
                "admin",         // username
                "admin123",      // password
                topics
        );

        DeviceConfigDataDTO data = new DeviceConfigDataDTO(
                device.getSerialNumber(),
                deviceConfig.getAudioLang(),
                deviceConfig.getHeartbeatInterval(),
                mqttConfig
        );

        return new DeviceConfigResponse(
                true,
                200,
                "Device configuration retrieved successfully",
                data,
                Instant.now().toString()
        );
    }
}
