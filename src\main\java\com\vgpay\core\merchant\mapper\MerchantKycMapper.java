package com.vgpay.core.merchant.mapper;

import com.vgpay.core.merchant.dto.MerchantDTO;
import com.vgpay.core.merchant.dto.MerchantKycDTO;
import com.vgpay.core.merchant.model.MerchantKYC;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring")
public interface MerchantKycMapper {

    MerchantKycDTO toDto(MerchantKYC merchantKYC);

    MerchantKYC toEntity(MerchantKycDTO dto);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void updateEntityFromDto(MerchantKycDTO dto, @MappingTarget MerchantKYC entity);

}
