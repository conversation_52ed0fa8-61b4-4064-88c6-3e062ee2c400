package com.vgpay.core.device.mapper;

import com.vgpay.core.device.dto.DeviceConfigDTO;
import com.vgpay.core.device.dto.DeviceDTO;
import com.vgpay.core.device.model.Device;
import com.vgpay.core.device.model.DeviceConfig;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring")
public interface DeviceConfigMapper {
    DeviceConfigDTO toDto(DeviceConfig entity);
    DeviceConfig toEntity(DeviceConfigDTO dto);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void updateDeviceConfigFromDto(DeviceConfigDTO dto, @MappingTarget DeviceConfig entity);
}
