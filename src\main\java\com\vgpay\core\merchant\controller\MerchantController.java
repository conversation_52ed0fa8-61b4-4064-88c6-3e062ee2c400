package com.vgpay.core.merchant.controller;

import com.vgpay.core.merchant.dto.MerchantDTO;
import com.vgpay.core.merchant.service.MerchantService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/merchants")
public class MerchantController {

    private final MerchantService merchantService;

    public MerchantController(MerchantService merchantService) {
        this.merchantService = merchantService;
    }

    @PostMapping
    public ResponseEntity<MerchantDTO> createMerchant(@RequestBody MerchantDTO dto) {
        return ResponseEntity.ok(merchantService.createMerchant(dto));
    }

    @PutMapping("/{id}")
    public ResponseEntity<MerchantDTO> updateMerchant(@PathVariable Long id, @RequestBody MerchantDTO dto) {
        return ResponseEntity.ok(merchantService.updateMerchant(id, dto));
    }

    @GetMapping("/all")
    public ResponseEntity<List<MerchantDTO>> getAllMerchants() {
        return ResponseEntity.ok(merchantService.getAllMerchants());
    }

    @GetMapping("/{id}")
    public ResponseEntity<MerchantDTO> getMerchantById(@PathVariable Long id) {
        return ResponseEntity.ok(merchantService.getMerchantById(id));
    }

    @GetMapping("/code/{code}")
    public ResponseEntity<MerchantDTO> getMerchantByCode(@PathVariable String code) {
        return ResponseEntity.ok(merchantService.getMerchantByCode(code));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<String> deleteMerchant(@PathVariable Long id) {
        merchantService.deleteMerchant(id);
        return ResponseEntity.ok("Merchant deleted successfully");
    }
}
