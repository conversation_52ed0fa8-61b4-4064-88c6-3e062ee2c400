package com.vgpay.core.common;

import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

@Component
public class MqttPublisher {
    private final MqttClient client;
    private final MqttProperties props;

    public MqttPublisher(MqttProperties props) throws MqttException {
        this.props = props;
//        client = new MqttClient("tcp://localhost:1883", "vgpay-backend");
//        client = new MqttClient("ws://************:8083", "vgpay-backend");
//        client = new MqttClient("ws://**************:8083/mqtt", "vgpay-backend", new MemoryPersistence());
        client = new MqttClient(props.getBrokerUri(), "vgpay-backend", new MemoryPersistence());
        MqttConnectOptions options = new MqttConnectOptions();
        options.setAutomaticReconnect(true);
        options.setCleanSession(true);
        options.setKeepAliveInterval(30);
        client.connect(options);

        System.out.println("-----------------Connected to MQTT broker:-------------------- " + props.getBrokerUri());
    }

    private void publishDynamic(String topicPattern, String serial, String payload) {
        if (client == null || !client.isConnected()) {
            System.err.println("-----------------MQTT not connected, skipping publish---------------------------");
            return;
        }
        try {
            String topic = topicPattern.replace("{serial}", serial);
            MqttMessage message = new MqttMessage(payload.getBytes(StandardCharsets.UTF_8));
            message.setQos(props.getQos());
            client.publish(topic, message);
            System.out.println("---------------------Published to------------------- " + topic + " => " + payload);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void publishConfig(String serial, String payload) {
        publishDynamic(props.getTopics().get("publish").get("config"), serial, payload);
    }

    public void publishPaymentStatus(String serial, String payload) {
        publishDynamic(props.getTopics().get("publish").get("payment_status"), serial, payload);
    }

    public void publishNotify(String serial, String payload) {
        publishDynamic(props.getTopics().get("publish").get("notify"), serial, payload);
    }

    public void publishBroadcast(String payload) {
        // broadcast doesn’t need serial
        publishDynamic(props.getTopics().get("publish").get("broadcast"), "all", payload);
    }

    public void publishFirmwareUpdate(String serial, String payload) {
        publishDynamic(props.getTopics().get("publish").get("firmware_update"), serial, payload);
    }

    public void sendCommand(String serial, String command) {
        String payload = "{ \"command\": \"" + command + "\" }";
        publishDynamic(props.getTopics().get("publish").get("command"), serial, payload);
    }
}
