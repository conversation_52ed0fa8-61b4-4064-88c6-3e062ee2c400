package com.vgpay.core.merchant.model;

import jakarta.persistence.*;
import lombok.*;

import java.time.Instant;

@Entity
@Table(name = "merchant")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Merchant {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;  // PK: Unique ID for each merchant

    @Column(nullable = false, unique = true)
    private String code; // Unique for external API communication

    @Column(nullable = false)
    private String businessName; // Business legal name

    @Column(nullable = false)
    private String contactName; // Merchant owner contact person

    private String phoneNumber;
    private String email;
    private String address;

    @Enumerated(EnumType.STRING)
    private MerchantStatus status; // KYC_PENDING/ACTIVE/INACTIVE/DELETED

    private Long bankPartnerId;    // FK → BankPartner.id

    private Instant createdAt;
    private Instant updatedAt;

}
