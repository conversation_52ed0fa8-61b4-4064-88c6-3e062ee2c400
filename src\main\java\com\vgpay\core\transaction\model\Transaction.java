package com.vgpay.core.transaction.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.Instant;

@Entity
@Table(name = "transactions")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Transaction {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, unique = true)
    private String transactionId;      // Internal Txn ID(Gateway / UPI reference) (Unique transaction reference from the gateway or UPI )

    @Column(unique = true)
    private String utr;                // Bank UPI transaction ref ( Unique Transaction Reference from bank/UPI)

    @Column(nullable = false)
    private Long merchantId;           // FK → Merchant.id


    @Column(nullable = false)
    private Long deviceId;            // FK → device.id

    @Column(nullable = false)
    private BigDecimal amount;        // Transaction amount

    private String currency;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private TransactionStatus status; // SUCCESS/FAILED/PENDING

    @Column(name = "payer_vpa")
    private String payerVpa;          // (Payer’s UPI address)

    @Column(name = "payer_name")
    private String payerName;

    @Column(name = "gateway_response")
    private String gatewayResponse;    // webhook JSON

    @Column(name = "acknowledged_at")
    private String acknowledgedAt;     // When your system/device acknowledged or notified the merchant

    private String remarks;            // Any remarks or additional information

    @Column(name = "created_at")
    private Instant createdAt;
}

