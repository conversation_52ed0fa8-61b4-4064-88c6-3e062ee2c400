package com.vgpay.core.device.dto;

import com.vgpay.core.device.model.MerchantDeviceStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MerchantDeviceDTO {
    private Long id;
    private Long merchantId;
    private Long deviceId;
    private String upiId;
    private MerchantDeviceStatus status;
    private Instant createdAt;
    private Instant updatedAt;
}
