package com.vgpay.core.device.controller;

import com.vgpay.core.device.dto.DeviceStatusLogDTO;
import com.vgpay.core.device.service.DeviceStatusLogService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/device-status-logs")
public class DeviceStatusLogController {

    private final DeviceStatusLogService service;

    public DeviceStatusLogController(DeviceStatusLogService service) {
        this.service = service;
    }

    @PostMapping
    public ResponseEntity<DeviceStatusLogDTO> create(@RequestBody DeviceStatusLogDTO dto) {
        return ResponseEntity.ok(service.create(dto));
    }

    @PutMapping("/{id}")
    public ResponseEntity<DeviceStatusLogDTO> update(@PathVariable Long id, @RequestBody DeviceStatusLogDTO dto) {
        return ResponseEntity.ok(service.update(id, dto));
    }

    @GetMapping("/device/{deviceId}")
    public ResponseEntity<List<DeviceStatusLogDTO>> getByDeviceId(@PathVariable Long deviceId) {
        return ResponseEntity.ok(service.getLogsByDeviceId(deviceId));
    }

    @GetMapping("/{id}")
    public ResponseEntity<DeviceStatusLogDTO> getById(@PathVariable Long id) {
        return ResponseEntity.ok(service.getById(id));
    }
}
