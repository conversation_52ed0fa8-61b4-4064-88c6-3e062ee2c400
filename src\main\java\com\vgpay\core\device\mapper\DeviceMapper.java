package com.vgpay.core.device.mapper;

import com.vgpay.core.device.dto.DeviceDTO;
import com.vgpay.core.device.model.Device;
import com.vgpay.core.merchant.dto.MerchantDTO;
import com.vgpay.core.merchant.model.Merchant;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring")
public interface DeviceMapper {
    DeviceDTO toDto(Device device);
    Device toEntity(DeviceDTO dto);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void updateDeviceFromDto(DeviceDTO dto, @MappingTarget Device entity);

}
