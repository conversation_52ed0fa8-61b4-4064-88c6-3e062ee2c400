package com.vgpay.core.merchant.dto;

import com.vgpay.core.merchant.model.MerchantStatus;
import lombok.Data;

import java.time.Instant;

@Data  // Generates getters, setters, equals, hashCode, toString
public class MerchantDTO {
    private Long id;
    private String code;
    private String businessName;
    private String contactName;
    private String phoneNumber;
    private String email;
    private String address;
    private MerchantStatus status;
    private Long bankPartnerId;
    private Instant createdAt;
    private Instant updatedAt;
}
