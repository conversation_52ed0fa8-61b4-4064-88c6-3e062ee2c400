package com.vgpay.core.device.repository;
import com.vgpay.core.device.model.Device;
import org.springframework.data.jpa.repository.JpaRepository;
import java.util.Optional;

public interface DeviceRepository extends JpaRepository<Device, Long> {

    Optional<Device> findByCode(String code);

    Optional<Device> findBySerialNumber(String serialNumber);

    Optional<Device> findByImei(String imei);

}