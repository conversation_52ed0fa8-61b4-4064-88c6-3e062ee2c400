package com.vgpay.core.merchant.mapper;

import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;

import com.vgpay.core.merchant.model.Merchant;
import com.vgpay.core.merchant.dto.MerchantDTO;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring")
public interface MerchantMapper {

    MerchantDTO toDto(Merchant merchant);

    Merchant toEntity(MerchantDTO dto);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void updateMerchantFromDto(MerchantDTO dto, @MappingTarget Merchant entity);
}
