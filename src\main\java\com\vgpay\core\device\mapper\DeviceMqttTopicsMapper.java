package com.vgpay.core.device.mapper;

import com.vgpay.core.device.dto.DeviceMqttTopicsDTO;
import com.vgpay.core.device.model.DeviceMqttTopics;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring")
public interface DeviceMqttTopicsMapper {
    DeviceMqttTopicsDTO toDto(DeviceMqttTopics entity);

    DeviceMqttTopics toEntity(DeviceMqttTopicsDTO dto);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void updateDeviceMqttTopicsFromDto(DeviceMqttTopicsDTO dto, @MappingTarget DeviceMqttTopics entity);
}
