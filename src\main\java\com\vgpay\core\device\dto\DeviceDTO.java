package com.vgpay.core.device.dto;

import com.vgpay.core.device.model.DeviceStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeviceDTO {
    private Long id;
    private String code;
    private String serialNumber;
    private String imei;
    private String simNumber;
    private String firmwareVersion;
    private DeviceStatus status;
    private Instant lastSeenAt;
    private Instant createdAt;
    private Instant updatedAt;
}
