package com.vgpay.core.merchant.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.Instant;

@Entity
@Table(name = "merchant_upi")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class MerchantUPI {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;   // PK

    @Column(name = "merchant_id", nullable = false)
    private Long merchantId;   // FK → Merchant.id

    @Column(name = "upi_id", nullable = false, unique = true)
    private String upiId;   // UPI ID returned from bank/PSP

    @Column(name = "created_at")
    private Instant createdAt;

}
