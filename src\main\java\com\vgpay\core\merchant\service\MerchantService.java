package com.vgpay.core.merchant.service;

import com.vgpay.core.merchant.dto.MerchantDTO;
import com.vgpay.core.merchant.mapper.MerchantMapper;
import com.vgpay.core.merchant.model.Merchant;
import com.vgpay.core.merchant.model.MerchantStatus;
import com.vgpay.core.merchant.repository.MerchantRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.time.Instant;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class MerchantService {

    private final MerchantRepository merchantRepository;
    private final MerchantMapper merchantMapper;

    public MerchantService(MerchantRepository merchantRepository, MerchantMapper merchantMapper) {
        this.merchantRepository = merchantRepository;
        this.merchantMapper = merchantMapper;
    }

    public MerchantDTO createMerchant(MerchantDTO dto) {
        Merchant merchant = merchantMapper.toEntity(dto);
        merchant = merchantRepository.save(merchant);
        return merchantMapper.toDto(merchant);
    }

    public List<MerchantDTO> getAllMerchants() {
        return merchantRepository.findAll()
                .stream()
                .filter(merchant -> merchant.getStatus() != MerchantStatus.INACTIVE)
                .map(merchantMapper::toDto)
                .collect(Collectors.toList());
    }

    public MerchantDTO getMerchantById(Long id) {
        return merchantRepository.findById(id)
                .map(merchantMapper::toDto)
                .orElse(null);
    }

    public MerchantDTO getMerchantByCode(String code) {
        return merchantRepository.findByCode(code)
                .map(merchantMapper::toDto)
                .orElseThrow(() -> new RuntimeException("Merchant not found"));
    }


    public MerchantDTO updateMerchant(Long id, MerchantDTO dto) {
        Merchant merchant = merchantRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Merchant not found"));

        // MapStruct helper → updates only non-null fields
        merchantMapper.updateMerchantFromDto(dto, merchant);
//        merchant.setUpdatedAt(Instant.now());
        merchant = merchantRepository.save(merchant);
        return merchantMapper.toDto(merchant);

    }

    public void deleteMerchant(Long id) {
        Merchant merchant = merchantRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Merchant not found"));
        if (merchant.getStatus() != MerchantStatus.INACTIVE){
            merchant.setStatus(MerchantStatus.INACTIVE);
            merchantRepository.save(merchant);
        }
    }
}
