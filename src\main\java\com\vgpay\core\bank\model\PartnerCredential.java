package com.vgpay.core.bank.model;

import jakarta.persistence.*;
import lombok.*;

import java.time.Instant;

@Entity
@Table(name = "partner_credentials")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PartnerCredential {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private Long partnerId;

    @Enumerated(EnumType.STRING)
    private AuthType authType;

    private String apiKey;       // encrypted (store as is, encrypt at service layer)
    private String clientId;     // for OAuth2
    private String clientSecret; // encrypted, optional for OAuth2
    private String certificate;  // encrypted, for mTLS

    private Integer version;

    @Enumerated(EnumType.STRING)
    private CredentialStatus status;

    private Instant createdAt;
    private Instant updatedAt;


}
