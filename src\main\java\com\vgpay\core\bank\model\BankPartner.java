package com.vgpay.core.bank.model;

import jakarta.persistence.*;
import lombok.*;

import java.time.Instant;

@Entity
@Table(name = "bank_partners")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BankPartner {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String name;

    @Column(nullable = false, unique = true)
    private String pspCode;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private PartnerType type; // BANK or PAYMENT_GATEWAY

    private String apiBaseUrl;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private PartnerStatus status;

    private Instant createdAt;

    private Instant updatedAt;

}
