package com.vgpay.core.device.service;

import com.vgpay.core.device.dto.DeviceStatusLogDTO;
import com.vgpay.core.device.mapper.DeviceStatusLogMapper;
import com.vgpay.core.device.model.DeviceStatusLog;
import com.vgpay.core.device.repository.DeviceStatusLogRepository;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class DeviceStatusLogService {
    private final DeviceStatusLogRepository repository;
    private final DeviceStatusLogMapper mapper;

    public DeviceStatusLogService(DeviceStatusLogRepository repository, DeviceStatusLogMapper mapper) {
        this.repository = repository;
        this.mapper = mapper;
    }

    public DeviceStatusLogDTO create(DeviceStatusLogDTO dto) {
        DeviceStatusLog entity = mapper.toEntity(dto);
        return mapper.toDto(repository.save(entity));
    }

    public DeviceStatusLogDTO update(Long id, DeviceStatusLogDTO dto) {
        DeviceStatusLog deviceStatusLog = repository.findById(id)
                .orElseThrow(() -> new RuntimeException("DeviceStatusLog not found"));
        mapper.updateDeviceStatusLogFromDto(dto, deviceStatusLog);
        return mapper.toDto(repository.save(deviceStatusLog));
    }

    public List<DeviceStatusLogDTO> getLogsByDeviceId(Long deviceId) {
        return repository.findByDeviceId(deviceId)
                .stream()
                .map(mapper::toDto)
                .collect(Collectors.toList());
    }

    public DeviceStatusLogDTO getById(Long id) {
        return repository.findById(id)
                .map(mapper::toDto)
                .orElse(null);
    }
}
