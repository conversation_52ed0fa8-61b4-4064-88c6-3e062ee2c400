package com.vgpay.core.device.repository;

import com.vgpay.core.device.model.MerchantDevice;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

public interface MerchantDeviceRepository extends JpaRepository<MerchantDevice, Long> {

    List<MerchantDevice> findByMerchantId(Long merchantId);

    List<MerchantDevice> findByDeviceId(Long deviceId);
}
