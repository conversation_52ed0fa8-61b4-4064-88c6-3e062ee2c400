package com.vgpay.core.device.mapper;

import com.vgpay.core.device.dto.DeviceStatusLogDTO;
import com.vgpay.core.device.model.DeviceStatusLog;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring")
public interface DeviceStatusLogMapper {
    DeviceStatusLogDTO toDto(DeviceStatusLog entity);
    DeviceStatusLog toEntity(DeviceStatusLogDTO dto);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void updateDeviceStatusLogFromDto(DeviceStatusLogDTO dto, @MappingTarget DeviceStatusLog entity);
}
