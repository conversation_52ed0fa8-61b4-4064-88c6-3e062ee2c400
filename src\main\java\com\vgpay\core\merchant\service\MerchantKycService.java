package com.vgpay.core.merchant.service;

import com.vgpay.core.merchant.dto.MerchantKycDTO;
import com.vgpay.core.merchant.mapper.MerchantKycMapper;
import com.vgpay.core.merchant.model.KycStatus;
import com.vgpay.core.merchant.model.MerchantKYC;
import com.vgpay.core.merchant.repository.MerchantKycRepository;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class MerchantKycService {

    private final MerchantKycRepository repository;
    private final MerchantKycMapper mapper;

    public MerchantKycService(MerchantKycRepository repository, MerchantKycMapper mapper) {
        this.repository = repository;
        this.mapper = mapper;
    }

    public MerchantKycDTO createMerchantKyc(MerchantKycDTO dto) {
        MerchantKYC merchantKYC = mapper.toEntity(dto);
        merchantKYC = repository.save(merchantKYC);
        return mapper.toDto(merchantKYC);
    }


    public MerchantKycDTO updateMerchantKyc(Long id, MerchantKycDTO dto) {
        MerchantKYC merchantKYC = repository.findById(id)
                .orElseThrow(() -> new RuntimeException("MerchantKYC not found"));
        mapper.updateEntityFromDto(dto, merchantKYC);
        merchantKYC = repository.save(merchantKYC);
        return mapper.toDto(merchantKYC);
    }

    public MerchantKycDTO getMerchantKycById(Long id) {
        return repository.findById(id)
                .map(mapper::toDto)
                .orElseThrow(() -> new RuntimeException("MerchantKYC not found"));
    }

//    public void deleteMerchantKyc(Long id) {
//      MerchantKYC merchantKYC = repository.findById(id)
//              .orElseThrow(() -> new RuntimeException("MerchantKYC not found"));
//      if (merchantKYC.getKycStatus() != KycStatus.REJECTED){
//          merchantKYC.setKycStatus(KycStatus.REJECTED);
//          repository.save(merchantKYC);
//      }
//    }

    public List<MerchantKycDTO> getAllMerchantKyc() {
        return repository.findAll()
                .stream()
                .map(mapper::toDto)
                .collect(Collectors.toList());
    }



}
