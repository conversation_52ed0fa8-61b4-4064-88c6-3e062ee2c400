package com.vgpay.core.common;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vgpay.core.device.model.DeviceStatusLog;
import com.vgpay.core.device.repository.DeviceRepository;
import com.vgpay.core.device.repository.DeviceStatusLogRepository;
import com.vgpay.core.transaction.repository.TransactionRepository;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.UUID;

@Component
public class MqttSubscriber {

    private final MqttProperties props;
    private final DeviceRepository deviceRepository;
    private final DeviceStatusLogRepository statusLogRepository;
    private final TransactionRepository transactionRepository;

    private final ObjectMapper objectMapper = new ObjectMapper();

    public MqttSubscriber(MqttProperties props, DeviceRepository deviceRepository, DeviceStatusLogRepository statusLogRepository,
                          TransactionRepository transactionRepository) {
        this.props = props;
        this.deviceRepository = deviceRepository;
        this.statusLogRepository = statusLogRepository;
        this.transactionRepository = transactionRepository;
    }

    private MqttClient client;

    @PostConstruct
    public void start() throws Exception {
        String clientID = props.getClientIdPrefix() + "-" + UUID.randomUUID();
        client = new MqttClient(props.getBrokerUri(), clientID);
        System.out.println("Broker URI ---------------------------------------- " + props.getBrokerUri());

        MqttConnectOptions options = new MqttConnectOptions();
        options.setCleanSession(true);
        options.setAutomaticReconnect(true);
        options.setKeepAliveInterval(30);

        if (props.getUsername() != null && !props.getUsername().isBlank()) {
            options.setUserName(props.getUsername());
        }
        if (props.getPassword() != null && !props.getPassword().isBlank()) {
            options.setPassword(props.getPassword().toCharArray());
        }

        client.connect(options);
        System.out.println("++++++++++++++++++ MQTT subscriber connected ++++++++++++++++++");

        // Subscribe to each topic pattern with its handler
        // Subscribe using wildcard topics
        // Subscribe to device → backend topics
        client.subscribe(props.getTopics().get("subscribe").get("status"), props.getQos(), this::handleStatus);
        client.subscribe(props.getTopics().get("subscribe").get("ack"), props.getQos(), this::handleAck);
        client.subscribe(props.getTopics().get("subscribe").get("logs"), props.getQos(), this::handleLogs);
        client.subscribe(props.getTopics().get("subscribe").get("boot"), props.getQos(), this::handleBoot);
        client.subscribe(props.getTopics().get("subscribe").get("lwt"), props.getQos(), this::handleLwt);

    }

    @PreDestroy
    public void stop() {
        try {
            if (client != null && client.isConnected()) client.disconnect();
        } catch (Exception ignored) {
        }
    }

    /* ---------------- Handlers ---------------- */

    // vgpay/device/{serial}/heartbeat
    public void handleStatus(String topic, MqttMessage message) {
        try {
            String serial = extractSerial(topic, "heartbeat");
            String json = new String(message.getPayload(), StandardCharsets.UTF_8);
            JsonNode root = objectMapper.readTree(json);

            String battery = text(root, "batteryLevel");
            String signal = text(root, "networkStrength");
            String fw = text(root, "firmwareVersion");
            Instant ts = parseInstant(root.path("timestamp").asText(null));

            // Debug print to confirm
            System.out.println(" -----------------[heartbeat]------------------ " + topic + " => " + json);

            deviceRepository.findBySerialNumber(serial).ifPresent(d -> {
                d.setLastSeenAt(Instant.now());
                if (fw != null && !fw.isBlank()) d.setFirmwareVersion(fw);
                deviceRepository.save(d);

                DeviceStatusLog log = DeviceStatusLog.builder()
                        .deviceId(d.getId())
                        .batteryLevel(battery)
                        .networkStrength(signal)
                        .firmwareVersion(fw)
                        .timestamp(ts != null ? ts : Instant.now())
                        .build();
                statusLogRepository.save(log);
            });

        } catch (Exception e) {
            System.err.println(" Error handling heartbeat message: " + e.getMessage());
            e.printStackTrace();
        }
    }


    // vgpay/device/{serial}/ack
    private void handleAck(String topic, MqttMessage msg) {
        try {
            String serial = extractSerial(topic, "ack");   // <-- FIXED
            System.out.println("serial from ACK: " + serial);

            String json = new String(msg.getPayload(), StandardCharsets.UTF_8);
            JsonNode root = objectMapper.readTree(json);

            String txnId = text(root, "transactionId");
            String ackStr = text(root, "ackStatus"); // SPOKEN/FAILED
            Instant ts = parseInstant(root.path("timestamp").asText(null));

            if (txnId == null || ackStr == null) {
                System.err.println("ACK missing fields (transactionId/ackStatus)");
                return;
            }

            deviceRepository.findBySerialNumber(serial).ifPresent(d -> {
                transactionRepository.findByTransactionId(txnId).ifPresent(t -> {
                    // mark ack status inside Transaction
                    if ("SPOKEN".equalsIgnoreCase(ackStr)) {
                        t.setAcknowledgedAt((ts != null ? ts : Instant.now()).toString());
                    } else {
                        // optional: mark FAILED or leave null
                        t.setAcknowledgedAt("FAILED@" + Instant.now());
                    }
                    transactionRepository.save(t);
                });
            });

            System.out.println("[ack] " + topic + " => " + json);

        } catch (Exception e) {
            System.err.println("Error handling ack message: " + e.getMessage());
            e.printStackTrace();
        }
    }


    // vgpay/device/{serial}/logs
    private void handleLogs(String topic, MqttMessage msg) {
        try {
            String serial = extractSerial(topic, "logs");   // <-- use serial extractor
            System.out.println("serial from LOG: " + serial);

            String json = new String(msg.getPayload(), StandardCharsets.UTF_8);
            JsonNode root = objectMapper.readTree(json);

            String message = text(root, "message");
            String level = text(root, "level");        // INFO/WARN/ERROR
            Instant ts = parseInstant(root.path("timestamp").asText(null));

            deviceRepository.findBySerialNumber(serial).ifPresent(d -> {
                DeviceStatusLog log = DeviceStatusLog.builder()
                        .deviceId(d.getId())   // save using resolved ID
                        .batteryLevel(null)
                        .networkStrength(level != null ? ("LOG:" + level) : "LOG")
                        .firmwareVersion(null)
                        .timestamp(ts != null ? ts : Instant.now())
                        .build();
                statusLogRepository.save(log);

                System.out.println("[logs] " + topic + " => " + (message != null ? message : json));
            });

        } catch (Exception e) {
            System.err.println(" Error handling logs message: " + e.getMessage());
            e.printStackTrace();
        }
    }


    // vgpay/device/{serial}/boot
    private void handleBoot(String topic, MqttMessage msg) {
        try {
            String serial = extractSerial(topic, "boot");   // use topic-based serial
            String json = new String(msg.getPayload(), StandardCharsets.UTF_8);
            JsonNode root = objectMapper.readTree(json);

            String firmware = text(root, "firmware_version");
            Instant ts = parseInstant(root.path("timestamp").asText(null));

            deviceRepository.findBySerialNumber(serial).ifPresent(d -> {
                d.setFirmwareVersion(firmware);
                d.setLastSeenAt(ts != null ? ts : Instant.now());
                // d.setStatus(DeviceStatus.ACTIVE);
                deviceRepository.save(d);
            });

            System.out.println("[boot] serial=" + serial + " => " + json);
        } catch (Exception e) {
            System.err.println(" Error handling boot message: " + e.getMessage());
            e.printStackTrace();
        }
    }

    // vgpay/device/{serial}/lwt
    private void handleLwt(String topic, MqttMessage msg) {
        try {
            String serial = extractSerial(topic, "lwt");   // use topic-based serial
            String json = new String(msg.getPayload(), StandardCharsets.UTF_8);
            JsonNode root = objectMapper.readTree(json);

            Instant ts = parseInstant(root.path("timestamp").asText(null));

            deviceRepository.findBySerialNumber(serial).ifPresent(d -> {
                // d.setStatus(DeviceStatus.INACTIVE);
                d.setLastSeenAt(ts != null ? ts : Instant.now());
                deviceRepository.save(d);
            });

            System.out.println("[lwt] serial=" + serial + " => " + json);
        } catch (Exception e) {
            System.err.println(" Error handling lwt message: " + e.getMessage());
            e.printStackTrace();
        }
    }


    /* ------------------------------------- Helpers -------------------------------- */

    private Long extractDeviceId(String topic, String tail) {
        // topics look like: devices/{id}/{tail}
        // split and take the second segment as id
        // be defensive for odd topics
        String[] parts = topic.split("/");
        // expected ["devices", "{id}", "{tail}"]
        if (parts.length >= 3 && "devices".equals(parts[0]) && tail.equals(parts[2])) {
            return Long.valueOf(parts[1]);
        }
        // fallback: try to parse any numeric segment
        for (String p : parts) {
            try {
                return Long.valueOf(p);
            } catch (NumberFormatException ignored) {
            }
        }
        throw new IllegalArgumentException("Cannot extract deviceId from topic: " + topic);
    }

    private String text(JsonNode node, String field) {
        return node.hasNonNull(field) ? node.get(field).asText() : null;
    }

    private Instant parseInstant(String iso) {
        if (iso == null || iso.isBlank()) return null;
        try {
            return Instant.parse(iso);
        } catch (Exception e) {
            return null;
        }
    }

    // NEW helper for serial number based topics
    private String extractSerial(String topic, String tail) {
        // expected: vgpay/device/{serial}/{tail}
        String[] parts = topic.split("/");
        if (parts.length >= 4 && "vgpay".equals(parts[0]) && "device".equals(parts[1]) && tail.equals(parts[3])) {
            return parts[2];
        }
        throw new IllegalArgumentException("Cannot extract serial from topic: " + topic);
    }


}
