package com.vgpay.core.bank.dto;

import com.vgpay.core.bank.model.PartnerStatus;
import com.vgpay.core.bank.model.PartnerType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class BankPartnerDTO {

    Long id;
    String name;
    String pspCode;
    PartnerType type;
    String apiBaseUrl;
    PartnerStatus status;
    Instant createdAt;
    Instant updatedAt;


}
