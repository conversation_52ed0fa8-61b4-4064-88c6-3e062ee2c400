package com.vgpay.core.device.controller;

import com.vgpay.core.device.dto.DeviceConfigDTO;
import com.vgpay.core.device.service.DeviceConfigService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/deviceConfig")
public class DeviceConfigController {

    private final DeviceConfigService deviceConfigService;

    public DeviceConfigController(DeviceConfigService deviceConfigService) {
        this.deviceConfigService = deviceConfigService;
    }

    @PostMapping
    public ResponseEntity<DeviceConfigDTO> createDeviceConfig(@RequestBody DeviceConfigDTO deviceConfigDTO) {
        return ResponseEntity.ok(deviceConfigService.save(deviceConfigDTO));
    }

    @GetMapping("/{id}")
    public ResponseEntity<DeviceConfigDTO> getDeviceConfigById(@PathVariable  Long id) {
        return ResponseEntity.ok(deviceConfigService.getDeviceConfigById(id));
    }

    @GetMapping("/device/{deviceId}")
    public ResponseEntity<DeviceConfigDTO> getDeviceConfigByDeviceId(@PathVariable  Long deviceId) {
        return ResponseEntity.ok(deviceConfigService.getDeviceConfigByDeviceId(deviceId));
    }

    @PutMapping("/{id}")
    public ResponseEntity<DeviceConfigDTO> updateDeviceConfig(@PathVariable  Long id, @RequestBody DeviceConfigDTO deviceConfigDTO) {
        return ResponseEntity.ok(deviceConfigService.update(id, deviceConfigDTO));
    }
}