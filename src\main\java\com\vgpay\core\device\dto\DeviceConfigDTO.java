package com.vgpay.core.device.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeviceConfigDTO {

    private Long id;
    private Long deviceId;
    private String audioLang;
    private String mqttBroker;
    private Integer heartbeatInterval;
    private Boolean retainedConfig;
    private Instant lastModified;

}
