# VG Pay Backend - Docker Setup

This guide explains how to run the VG Pay Backend application using Docker and Docker Compose.

## Prerequisites

- Docker (version 20.10 or later)
- Docker Compose (version 2.0 or later)

## Quick Start

1. **Clone the repository and navigate to the project directory:**
   ```bash
   cd vg-pay-backend
   ```

2. **Start all services:**
   ```bash
   docker-compose up -d
   ```

3. **Check the status of services:**
   ```bash
   docker-compose ps
   ```

4. **View logs:**
   ```bash
   # All services
   docker-compose logs -f
   
   # Specific service
   docker-compose logs -f vgpay-core
   ```

## Services

The Docker Compose setup includes:

### 1. VG Pay Core Application
- **Container:** `vgpay-core-app`
- **Port:** 8586
- **URL:** http://localhost:8586
- **API Documentation:** http://localhost:8586/swagger-ui.html

### 2. MySQL Database
- **Container:** `vgpay-mysql`
- **Port:** 3306
- **Database:** `vgpaydb`
- **Username:** `root`
- **Password:** `root`

### 3. MQTT Broker (Mosquitto)
- **Container:** `vgpay-mosquitto`
- **MQTT Port:** 1883
- **WebSocket Port:** 8083
- **HTTP WebSocket Port:** 9001

## Environment Variables

You can customize the application by modifying environment variables in `docker-compose.yml`:

### Database Configuration
- `SPRING_DATASOURCE_URL`
- `SPRING_DATASOURCE_USERNAME`
- `SPRING_DATASOURCE_PASSWORD`

### MQTT Configuration
- `MQTT_BROKER_URI`
- `MQTT_USERNAME`
- `MQTT_PASSWORD`

## Useful Commands

### Start services
```bash
docker-compose up -d
```

### Stop services
```bash
docker-compose down
```

### Rebuild and start
```bash
docker-compose up -d --build
```

### View logs
```bash
docker-compose logs -f [service-name]
```

### Access database
```bash
docker-compose exec mysql mysql -u root -proot vgpaydb
```

### Access application container
```bash
docker-compose exec vgpay-core sh
```

## Troubleshooting

### Application won't start
1. Check if all services are healthy:
   ```bash
   docker-compose ps
   ```

2. Check application logs:
   ```bash
   docker-compose logs vgpay-core
   ```

### Database connection issues
1. Ensure MySQL is healthy:
   ```bash
   docker-compose logs mysql
   ```

2. Check if the database is accessible:
   ```bash
   docker-compose exec mysql mysqladmin ping -h localhost -u root -proot
   ```

### MQTT connection issues
1. Check Mosquitto logs:
   ```bash
   docker-compose logs mosquitto
   ```

2. Test MQTT connection:
   ```bash
   docker-compose exec mosquitto mosquitto_pub -h localhost -t test -m "hello"
   ```

## Data Persistence

- **MySQL data:** Stored in Docker volume `mysql_data`
- **MQTT data:** Stored in Docker volume `mosquitto_data`

To remove all data:
```bash
docker-compose down -v
```

## Development

For development, you can:

1. **Mount source code as volume** (add to docker-compose.yml):
   ```yaml
   volumes:
     - ./src:/app/src
   ```

2. **Use development profile:**
   ```yaml
   environment:
     SPRING_PROFILES_ACTIVE: dev
   ```

3. **Enable hot reload** by rebuilding the container when code changes.

## Production Considerations

For production deployment:

1. **Use environment-specific configuration files**
2. **Set up proper secrets management**
3. **Configure resource limits**
4. **Set up monitoring and logging**
5. **Use external databases and message brokers**
6. **Implement proper backup strategies**
