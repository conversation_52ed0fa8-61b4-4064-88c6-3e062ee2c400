package com.vgpay.core.device.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vgpay.core.common.MqttPublisher;
import com.vgpay.core.common.MqttTopicResolver;
import com.vgpay.core.device.dto.*;
import com.vgpay.core.device.model.Device;
import com.vgpay.core.device.model.DeviceConfig;
import com.vgpay.core.device.repository.DeviceConfigRepository;
import com.vgpay.core.device.repository.DeviceRepository;
import com.vgpay.core.device.service.DeviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/device")
public class DeviceController {

    @Autowired
    public MqttPublisher mqttPublisher;

    @Autowired
    public MqttTopicResolver mqttTopicResolver;

    @Autowired
    public DeviceRepository deviceRepository;

    @Autowired
    public DeviceConfigRepository configRepository;

    private final DeviceService deviceService;

    public DeviceController(DeviceService deviceService) {
        this.deviceService = deviceService;
    }

    @PostMapping("/register")
    public ResponseEntity<DeviceRegisterResponse> register(@RequestBody DeviceRegisterRequest req) {
//        return ResponseEntity.ok(deviceService.registerDevice(req));
        return ResponseEntity.ok(deviceService.verifyDevice(req));
    }

    @PostMapping("/config")
    public ResponseEntity<DeviceConfigResponse> config(@RequestBody DeviceConfigRequest req) {
        DeviceConfigResponse response = deviceService.fetchConfig(req);

        // Build full payload dynamically
        Map<String, Object> payload = new HashMap<>();
        payload.put("serial_number", req.getSerialNumber());
        payload.put("audio_language", response.getData().getAudio_language());
        payload.put("heartbeat_interval", response.getData().getHeartbeat_interval());

        // Build mqtt_config
        Map<String, Object> mqttConfig = new HashMap<>();
        mqttConfig.put("broker_url", response.getData().getMqtt_config().getBroker_url());
        mqttConfig.put("port", response.getData().getMqtt_config().getPort());
        mqttConfig.put("username", "admin");
        mqttConfig.put("password", "admin123");

        // Resolve topics dynamically for this serial
        Map<String, Map<String, String>> topics = mqttTopicResolver.resolveTopics(req.getSerialNumber());
        mqttConfig.put("topics", topics);

        payload.put("mqtt_config", mqttConfig);

        try {
            // Convert Map to JSON string
            ObjectMapper mapper = new ObjectMapper();
            String jsonPayload = mapper.writeValueAsString(payload);

            // Publish over MQTT
            mqttPublisher.publishConfig(req.getSerialNumber(), jsonPayload);
            System.out.println("Published full config: " + jsonPayload);

        } catch (Exception e) {
            e.printStackTrace();
        }

        return ResponseEntity.ok(response);
    }


    @PostMapping
    public ResponseEntity<DeviceDTO> createDevice(@RequestBody DeviceDTO deviceDTO) {
        return ResponseEntity.ok(deviceService.createDevice(deviceDTO));
    }

    @PutMapping("/{id}")
    public ResponseEntity<DeviceDTO> updateDeivice(@PathVariable Long id, @RequestBody DeviceDTO deviceDTO) {
        return ResponseEntity.ok(deviceService.updateDevice(id, deviceDTO));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<String> deleteDevice(@PathVariable Long id) {
        deviceService.deleteDevice(id);
        return ResponseEntity.ok("Device deleted successfully");
    }

    @GetMapping("/{id}")
    public ResponseEntity<DeviceDTO> getDevice(@PathVariable Long id) {
        return ResponseEntity.ok(deviceService.getDevice(id));
    }

    @GetMapping("/all")
    public ResponseEntity<List<DeviceDTO>> getAllDevice() {
        return ResponseEntity.ok(deviceService.getAllDevice());
    }

    @GetMapping("/imei/{imei}")
    public ResponseEntity<DeviceDTO> getDeviceByImei(@PathVariable String imei) {
        return ResponseEntity.ok(deviceService.getDeviceByImei(imei));
    }

    @GetMapping("/code/{code}")
    public ResponseEntity<DeviceDTO> getDeviceByCode(@PathVariable String code) {
        return ResponseEntity.ok(deviceService.getDeviceByCode(code));
    }

    @GetMapping("/serialNumber/{serialNumber}")
    public ResponseEntity<DeviceDTO> getDeviceBySerialNumber(@PathVariable String serialNumber) {
        return ResponseEntity.ok(deviceService.getDeviceBySerialNumber(serialNumber));
    }

}
