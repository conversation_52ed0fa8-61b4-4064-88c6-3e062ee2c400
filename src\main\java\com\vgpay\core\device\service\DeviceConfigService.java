package com.vgpay.core.device.service;

import com.vgpay.core.device.dto.DeviceConfigDTO;
import com.vgpay.core.device.mapper.DeviceConfigMapper;
import com.vgpay.core.device.model.DeviceConfig;
import com.vgpay.core.device.repository.DeviceConfigRepository;
import org.springframework.stereotype.Service;

@Service
public class DeviceConfigService {

    private final DeviceConfigRepository deviceConfigRepository;
    private final DeviceConfigMapper deviceConfigMapper;

    public DeviceConfigService(DeviceConfigRepository deviceConfigRepository, DeviceConfigMapper deviceConfigMapper) {
        this.deviceConfigRepository = deviceConfigRepository;
        this.deviceConfigMapper = deviceConfigMapper;
    }

    public DeviceConfigDTO save(DeviceConfigDTO deviceConfigDTO) {
        return deviceConfigMapper.toDto(deviceConfigRepository.save(deviceConfigMapper.toEntity(deviceConfigDTO)));
    }

    public DeviceConfigDTO update(Long id, DeviceConfigDTO deviceConfigDTO) {
        DeviceConfig deviceConfig = deviceConfigRepository.findById(id).
                orElseThrow(() -> new RuntimeException("DeviceConfig not found"));
        deviceConfigMapper.updateDeviceConfigFromDto(deviceConfigDTO, deviceConfig);
        return deviceConfigMapper.toDto(deviceConfigRepository.save(deviceConfig));
    }

    public DeviceConfigDTO getDeviceConfigById(Long id) {
        return deviceConfigRepository.findById(id)
                .map(deviceConfigMapper::toDto)
                .orElseThrow(() -> new RuntimeException("DeviceConfig not found"));
    }

    public DeviceConfigDTO getDeviceConfigByDeviceId(Long deviceId) {
        return deviceConfigRepository.findByDeviceId(deviceId)
                .map(deviceConfigMapper::toDto)
                .orElseThrow(() -> new RuntimeException("DeviceConfig not found"));
    }

}
