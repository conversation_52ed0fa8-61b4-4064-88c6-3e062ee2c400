spring:
  datasource:
    url: ************************************************************************************************
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver

  jpa:
    hibernate:
#      ddl-auto: validate    # use 'update' in dev if you want auto schema creation
      ddl-auto: update    # use 'update' in dev if you want auto schema creation
    show-sql: true
    database-platform: org.hibernate.dialect.MySQL8Dialect

server:
  port: 8586
  address: 0.0.0.0     # listen on all network interfaces

logging:
  level:
    root: INFO
    com.vgpay: DEBUG

mqtt:
#  broker-uri: tcp://localhost:1883
#  broker-uri: ws://************:8083
  broker-uri: ws://**************:8083
  client-id-prefix: vgpay-core
  username: vg-payment
  password: vgpay@123
  qos: 1
#  topics:
#    status: vgpay/device/+/heartbeat
#    ack: vgpay/device/+/ack
#    logs: vgpay/device/+/logs
#    boot: vgpay/device/+/boot
#    lwt: vgpay/device/+/lwt
  topics:
    subscribe:
      status: vgpay/device/{serial}/heartbeat
      ack: vgpay/device/{serial}/ack
      logs: vgpay/device/{serial}/logs
      boot: vgpay/device/{serial}/boot
      lwt: vgpay/device/{serial}/lwt
    publish:
      config: vgpay/device/{serial}/config
      payment_status: vgpay/device/{serial}/payment/status
      notify: vgpay/device/{serial}/notify
      broadcast: vgpay/device/all/notify
      firmware_update: vgpay/device/{serial}/firmware/update
      command: vgpay/server/{serial}/command

