package com.vgpay.core.transaction.service;

import com.vgpay.core.common.MqttPublisher;
import com.vgpay.core.device.model.Device;
import com.vgpay.core.device.repository.DeviceRepository;
import com.vgpay.core.transaction.dto.TransactionDTO;
import com.vgpay.core.transaction.mapper.TransactionMapper;
import com.vgpay.core.transaction.model.Transaction;
import com.vgpay.core.transaction.repository.TransactionRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;

@Service
public class TransactionService {

    @Autowired
    DeviceRepository deviceRepository;

    private final TransactionRepository repository;
    private final TransactionMapper mapper;
    private final MqttPublisher mqttPublisher;

    public TransactionService(TransactionRepository repository, TransactionMapper mapper, MqttPublisher mqttPublisher) {
        this.repository = repository;
        this.mapper = mapper;
        this.mqttPublisher = mqttPublisher;
    }

    public TransactionDTO create(TransactionDTO dto) {
        Transaction txn = mapper.toEntity(dto);
        txn = repository.save(txn);

        // Lookup serial by deviceId
        String serial = deviceRepository.findById(dto.getDeviceId())
                .map(Device::getSerialNumber)
                .orElseThrow(() -> new RuntimeException("Device not found for ID " + dto.getDeviceId()));

        // Publish MQTT event after save
        String payload = """
                {
                  "transactionId": "%s",
                  "amount": %.2f,
                  "status": "%s",
                  "timestamp": "%s"
                }
                """.formatted(dto.getTransactionId(), dto.getAmount(), dto.getStatus(), Instant.now());
           mqttPublisher.publishPaymentStatus(serial, payload);

        return mapper.toDto(txn);
    }

    public TransactionDTO update(Long id,TransactionDTO dto) {
        Transaction txn = repository.findById(id)
                .orElseThrow(() -> new RuntimeException("Transaction not found"));
        mapper.updateTransactionFromDto(dto, txn);
        txn = repository.save(txn);
        return mapper.toDto(txn);
    }

    public TransactionDTO getTransactionById(Long id) {
        return repository.findById(id)
                .map(mapper::toDto)
                .orElseThrow(() -> new RuntimeException("Transaction not found"));
    }
}
