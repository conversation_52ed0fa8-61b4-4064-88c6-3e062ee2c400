package com.vgpay.core.common;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "mqtt")
public class MqttProperties {
    @Value("${mqtt.broker-uri}")
    private String brokerUri;

    @Value("${mqtt.client-id-prefix}")
    private String clientIdPrefix = "vgpay-core-sub";

    private String username;
    private String password;
    private int qos = 1;

    //    private Map<String, String> topics;

    private Map<String, Map<String, String>> topics;
    // topics.subscribe.*, topics.publish.*
}
