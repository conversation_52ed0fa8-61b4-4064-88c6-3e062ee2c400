package com.vgpay.core.transaction.mapper;

import com.vgpay.core.bank.dto.BankPartnerDTO;
import com.vgpay.core.bank.model.BankPartner;
import com.vgpay.core.transaction.dto.TransactionDTO;
import com.vgpay.core.transaction.model.Transaction;
import org.mapstruct.*;

@Mapper(componentModel = "spring")
public interface TransactionMapper {

    @Mapping(target = "createdAt", ignore = true) // handled in service
    Transaction toEntity(TransactionDTO dto);
    TransactionDTO toDto(Transaction entity);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void updateTransactionFromDto(TransactionDTO dto, @MappingTarget Transaction entity);

}
