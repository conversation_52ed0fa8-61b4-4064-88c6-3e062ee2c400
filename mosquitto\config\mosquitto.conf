# Mosquitto MQTT Broker Configuration

# General settings
persistence true
persistence_location /mosquitto/data/
log_dest file /mosquitto/log/mosquitto.log
log_dest stdout

# MQTT Protocol settings
listener 1883
protocol mqtt

# WebSocket settings for MQ<PERSON> over WebSocket
listener 8083
protocol websockets

# HTTP WebSocket listener
listener 9001
protocol websockets

# Security settings
allow_anonymous true

# Connection settings
max_connections -1
max_inflight_messages 20
max_queued_messages 100

# Logging
log_type error
log_type warning
log_type notice
log_type information
log_type debug

# Persistence settings
autosave_interval 1800
autosave_on_changes false

# Message size limits
message_size_limit 0

# Client settings
persistent_client_expiration 2m
