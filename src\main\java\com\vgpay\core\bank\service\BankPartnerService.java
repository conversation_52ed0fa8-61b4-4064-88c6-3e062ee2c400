package com.vgpay.core.bank.service;

import com.vgpay.core.bank.dto.BankPartnerDTO;
import com.vgpay.core.bank.mapper.BankPartnerMapper;
import com.vgpay.core.bank.model.BankPartner;
import com.vgpay.core.bank.model.PartnerStatus;
import com.vgpay.core.bank.repository.BankPartnerRepository;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class BankPartnerService {

    private final BankPartnerRepository bankPartnerRepository;
    private final BankPartnerMapper bankPartnerMapper;

    public BankPartnerService(BankPartnerRepository bankPartnerRepository, BankPartnerMapper bankPartnerMapper) {
        this.bankPartnerRepository = bankPartnerRepository;
        this.bankPartnerMapper = bankPartnerMapper;
    }

    public BankPartnerDTO createBankPartner(BankPartnerDTO dto) {
        BankPartner bankPartner = bankPartnerMapper.toEntity(dto);
        bankPartner = bankPartnerRepository.save(bankPartner);
        return bankPartnerMapper.toDto(bankPartner);
    }

    public BankPartnerDTO updateBankPartner(Long id, BankPartnerDTO dto) {
        BankPartner bankPartner = bankPartnerRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "BankPartner not found"));

        bankPartnerMapper.updateBankPartnerFromDto(dto, bankPartner);
        bankPartner = bankPartnerRepository.save(bankPartner);
        return bankPartnerMapper.toDto(bankPartner);
    }

    public BankPartnerDTO getBankPartner(Long id) {
        BankPartner bankPartner = bankPartnerRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "BankPartner not found"));
        return bankPartnerMapper.toDto(bankPartner);
    }

    public void deleteBankPartner(Long id) {
        BankPartner bankPartner = bankPartnerRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "BankPartner not found"));
        if (bankPartner.getStatus() != PartnerStatus.INACTIVE) {
            bankPartner.setStatus(PartnerStatus.INACTIVE);
            bankPartnerRepository.save(bankPartner);
        }
    }

    public List<BankPartnerDTO> getAllActiveBankPartners() {
        return bankPartnerRepository.findAll().stream()
                .filter(bankPartner -> bankPartner.getStatus() == PartnerStatus.ACTIVE)
                .map(bankPartnerMapper::toDto)
                .collect(Collectors.toList());
    }


}
