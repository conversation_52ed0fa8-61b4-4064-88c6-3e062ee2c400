package com.vgpay.core.common;

import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class MqttTopicResolver {

    private final MqttProperties props;

    public MqttTopicResolver(MqttProperties props) {
        this.props = props;
    }

    public Map<String, Map<String, String>> resolveTopics(String serial) {
        Map<String, Map<String, String>> resolved = new HashMap<>();

        props.getTopics().forEach((category, topics) -> {
            Map<String, String> resolvedMap = new HashMap<>();
            topics.forEach((name, pattern) -> {
                resolvedMap.put(name, pattern.replace("{serial}", serial));
            });
            resolved.put(category, resolvedMap);
        });

        return resolved;
    }
}

