package com.vgpay.core.merchant.mapper;

import com.vgpay.core.merchant.dto.MerchantDTO;
import com.vgpay.core.merchant.dto.MerchantUpiDTO;
import com.vgpay.core.merchant.model.Merchant;
import com.vgpay.core.merchant.model.MerchantUPI;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring")
public interface MerchantUPIMapper {

    MerchantUpiDTO toDto(MerchantUPI merchantUPI);
    MerchantUPI toEntity(MerchantUpiDTO merchantUpiDTO);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void updateMerchantUPIFromDto(MerchantUpiDTO dto, @MappingTarget MerchantUPI entity);

}
