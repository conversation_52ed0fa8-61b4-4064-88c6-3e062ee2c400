package com.vgpay.core.bank.controller;

import com.vgpay.core.bank.dto.PartnerCredentialDTO;
import com.vgpay.core.bank.service.PartnerCredentialService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/credentials")
public class PartnerCredentialController {

    private final PartnerCredentialService partnerCredentialService;

    public PartnerCredentialController(PartnerCredentialService partnerCredentialService) {
        this.partnerCredentialService = partnerCredentialService;
    }

    @PostMapping
    public ResponseEntity<PartnerCredentialDTO> createPartnerCredential(@RequestBody PartnerCredentialDTO dto) {
        return ResponseEntity.ok(partnerCredentialService.createPartnerCredential(dto));
    }

    @PutMapping("/{id}")
    public ResponseEntity<PartnerCredentialDTO> updatePartnerCredential(@PathVariable Long id, @RequestBody PartnerCredentialDTO dto) {
        return ResponseEntity.ok(partnerCredentialService.updatePartnerCredential(id, dto));
    }

    @GetMapping("/{id}")
    public ResponseEntity<PartnerCredentialDTO> getPartnerCredential(@PathVariable Long id) {
        return ResponseEntity.ok(partnerCredentialService.getPartnerCredential(id));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<String> deletePartnerCredential(@PathVariable Long id) {
        partnerCredentialService.deletePartnerCredential(id);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/all")
    public ResponseEntity<List<PartnerCredentialDTO>> getAllPartnerCredentials() {
        return ResponseEntity.ok(partnerCredentialService.getAllActivePartnerCredentials());

    }

}
