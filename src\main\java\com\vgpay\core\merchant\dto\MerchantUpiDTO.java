package com.vgpay.core.merchant.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MerchantUpiDTO {
    private Long id;          // null when creating
    private Long merchantId;  // reference to Merchant
    private String upiId;
    private Instant createdAt;  // auto-set by backend

}
