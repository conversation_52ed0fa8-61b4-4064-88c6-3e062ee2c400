package com.vgpay.core.device.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Response after device registration
 */
@Data
@NoArgsConstructor
@AllArgsConstructor

public class DeviceRegisterResponse {

    private boolean success;
    private int status;
    private String message;
    private String serialNumber;
    private String authToken;
    private String timestamp;

}
