package com.vgpay.core.merchant.service;

import com.vgpay.core.merchant.dto.MerchantUpiDTO;
import com.vgpay.core.merchant.mapper.MerchantUPIMapper;
import com.vgpay.core.merchant.model.MerchantUPI;
import com.vgpay.core.merchant.repository.MerchantUPIRepository;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class MerchantUPIService {


    private final MerchantUPIRepository repository;
    private final MerchantUPIMapper mapper;

    public MerchantUPIService(MerchantUPIRepository repository, MerchantUPIMapper mapper) {
        this.repository = repository;
        this.mapper = mapper;
    }

    public MerchantUpiDTO createMerchantUPI(MerchantUpiDTO merchantUpiDTO) {
        MerchantUPI enity = mapper.toEntity(merchantUpiDTO);
        return mapper.toDto(repository.save(enity));
    }

    public MerchantUpiDTO updateMerchantUPI(Long id, MerchantUpiDTO merchantUpiDTO) {
        MerchantUPI merchantUPI = repository.findById(id)
                .orElseThrow(() -> new RuntimeException("MerchantUPI not found"));
        mapper.updateMerchantUPIFromDto(merchantUpiDTO, merchantUPI);
        return mapper.toDto(repository.save(merchantUPI));
    }

    public MerchantUpiDTO getMerchantUPIById(Long id) {
        return repository.findById(id)
                .map(mapper::toDto)
                .orElseThrow(() -> new RuntimeException("MerchantUPI not found"));
    }

    public void deleteMerchantUPI(Long id) {
        repository.deleteById(id);
    }

   public List<MerchantUpiDTO> getAllMerchantUPIs() {
        return repository.findAll()
                .stream()
                .map(mapper::toDto)
                .collect(Collectors.toList());
    }

}
