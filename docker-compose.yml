services:
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: vgpaydb
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql

  mosquitto:
    image: eclipse-mosquitto:2.0
    ports:
      - "1883:1883"
      - "8083:8083"
    volumes:
      - ./mosquitto/config:/mosquitto/config

  vgpay-core:
    build: .
    ports:
      - "8083:8083"
    environment:
      SPRING_DATASOURCE_URL: ********************************************************************************************
      SPRING_DATASOURCE_USERNAME: root
      SPRING_DATASOURCE_PASSWORD: root
      MQTT_BROKER_URI: ws://mosquitto:8083
    depends_on:
      - mysql
      - mosquitto

volumes:
  mysql_data:
