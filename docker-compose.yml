version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: vgpay-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: vgpaydb
      MYSQL_USER: vgpay
      MYSQL_PASSWORD: vgpay123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - vgpay-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-proot"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # MQTT Broker (Eclipse Mosquitto)
  mosquitto:
    image: eclipse-mosquitto:2.0
    container_name: vgpay-mosquitto
    restart: unless-stopped
    ports:
      - "1883:1883"
      - "8083:8083"
      - "9001:9001"
    volumes:
      - ./mosquitto/config:/mosquitto/config
      - ./mosquitto/data:/mosquitto/data
      - ./mosquitto/log:/mosquitto/log
      - mosquitto_data:/mosquitto/data
    networks:
      - vgpay-network
    healthcheck:
      test: ["CMD", "mosquitto_pub", "-h", "localhost", "-t", "test", "-m", "test"]
      interval: 30s
      timeout: 10s
      retries: 3

  # VG Pay Core Application
  vgpay-core:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: vgpay-core-app
    restart: unless-stopped
    ports:
      - "8586:8586"
    environment:
      # Database configuration
      SPRING_DATASOURCE_URL: ********************************************************************************************
      SPRING_DATASOURCE_USERNAME: root
      SPRING_DATASOURCE_PASSWORD: root
      SPRING_DATASOURCE_DRIVER_CLASS_NAME: com.mysql.cj.jdbc.Driver
      
      # JPA/Hibernate configuration
      SPRING_JPA_HIBERNATE_DDL_AUTO: update
      SPRING_JPA_SHOW_SQL: true
      SPRING_JPA_DATABASE_PLATFORM: org.hibernate.dialect.MySQL8Dialect
      
      # Server configuration
      SERVER_PORT: 8586
      SERVER_ADDRESS: 0.0.0.0
      
      # Logging configuration
      LOGGING_LEVEL_ROOT: INFO
      LOGGING_LEVEL_COM_VGPAY: DEBUG
      
      # MQTT configuration
      MQTT_BROKER_URI: ws://mosquitto:8083
      MQTT_CLIENT_ID_PREFIX: vgpay-core
      MQTT_USERNAME: vg-payment
      MQTT_PASSWORD: vgpay@123
      MQTT_QOS: 1
      
      # Application name
      SPRING_APPLICATION_NAME: vgpay-core
    depends_on:
      mysql:
        condition: service_healthy
      mosquitto:
        condition: service_healthy
    networks:
      - vgpay-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8586/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

volumes:
  mysql_data:
    driver: local
  mosquitto_data:
    driver: local

networks:
  vgpay-network:
    driver: bridge
