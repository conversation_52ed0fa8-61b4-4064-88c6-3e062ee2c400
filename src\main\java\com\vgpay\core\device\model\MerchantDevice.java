package com.vgpay.core.device.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.Instant;

@Entity
@Table(name = "merchant_device")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class MerchantDevice {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;   // PK

    @Column(name = "merchant_id", nullable = false)
    private Long merchantId;   // FK → Merchant.id

    @Column(name = "device_id", nullable = false)
    private Long deviceId;   // FK → Device.id

    @Column(name = "upi_id", nullable = false)
    private String upiId;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private MerchantDeviceStatus status = MerchantDeviceStatus.ASSIGNED;

    @Column(name = "created_at")
    private Instant createdAt;

    @Column(name = "updated_at")
    private Instant updatedAt;
}
