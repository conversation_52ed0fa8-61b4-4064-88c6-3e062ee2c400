package com.vgpay.core.device.service;

import com.vgpay.core.device.dto.MerchantDeviceDTO;
import com.vgpay.core.device.mapper.MerchantDeviceMapper;
import com.vgpay.core.device.model.MerchantDevice;
import com.vgpay.core.device.repository.MerchantDeviceRepository;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class MerchantDeviceService {

    private final MerchantDeviceRepository merchantDeviceRepository;
    private final MerchantDeviceMapper merchantDeviceMapper;

    public MerchantDeviceService(MerchantDeviceRepository merchantDeviceRepository, MerchantDeviceMapper merchantDeviceMapper) {
        this.merchantDeviceRepository = merchantDeviceRepository;
        this.merchantDeviceMapper = merchantDeviceMapper;
    }

    public MerchantDeviceDTO assignDevice(MerchantDeviceDTO merchantDeviceDTO) {
        MerchantDevice merchantDevice = merchantDeviceMapper.toEntity(merchantDeviceDTO);
        MerchantDevice savedMerchantDevice = merchantDeviceRepository.save(merchantDevice);
        return merchantDeviceMapper.toDto(savedMerchantDevice);
    }

    public MerchantDeviceDTO getById(Long id) {
        MerchantDevice merchantDevice = merchantDeviceRepository.findById(id).orElse(null);
        return merchantDeviceMapper.toDto(merchantDevice);
    }

    public MerchantDeviceDTO updateMerchantDevice(Long id, MerchantDeviceDTO merchantDeviceDTO) {

        MerchantDevice merchantDevice = merchantDeviceRepository.findById(id).
                orElseThrow(() -> new RuntimeException("MerchantDevice not found"));

        merchantDeviceMapper.updateMerchantDeviceFromDto(merchantDeviceDTO, merchantDevice);
        MerchantDevice updatedMerchantDevice = merchantDeviceRepository.save(merchantDevice);
        return merchantDeviceMapper.toDto(updatedMerchantDevice);
    }

    public List<MerchantDeviceDTO> getAllMerchantDevice() {
        return merchantDeviceRepository.findAll()
                .stream().map(merchantDeviceMapper::toDto).toList();
    }

    public List<MerchantDeviceDTO> getMerchantDeviceByMerchantId(Long merchantId) {
        return merchantDeviceRepository.findByMerchantId(merchantId)
                .stream().map(merchantDeviceMapper::toDto).toList();
    }

    public List<MerchantDeviceDTO> getByDeviceId(Long deviceId) {
        return merchantDeviceRepository.findByDeviceId(deviceId).stream().map(merchantDeviceMapper::toDto).toList();
    }


    }
