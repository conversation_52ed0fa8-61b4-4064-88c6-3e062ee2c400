package com.vgpay.core.device.service;

import com.vgpay.core.device.dto.DeviceDTO;
import com.vgpay.core.device.dto.DeviceMqttTopicsDTO;
import com.vgpay.core.device.mapper.DeviceMqttTopicsMapper;
import com.vgpay.core.device.model.DeviceMqttTopics;
import com.vgpay.core.device.model.SubscriptionStatus;
import com.vgpay.core.device.repository.DeviceMqttTopicsRepository;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class DeviceMqttTopicsService {

    private final DeviceMqttTopicsRepository deviceMqttTopicsRepository;
    private final DeviceMqttTopicsMapper deviceMqttTopicsMapper;

    public DeviceMqttTopicsService(DeviceMqttTopicsRepository deviceMqttTopicsRepository, DeviceMqttTopicsMapper deviceMqttTopicsMapper) {
        this.deviceMqttTopicsRepository = deviceMqttTopicsRepository;
        this.deviceMqttTopicsMapper = deviceMqttTopicsMapper;
    }

    public DeviceMqttTopicsDTO createDeviceMqttTopics(DeviceMqttTopicsDTO deviceMqttTopicsDTO) {
        return deviceMqttTopicsMapper.toDto(deviceMqttTopicsRepository.save(deviceMqttTopicsMapper.toEntity(deviceMqttTopicsDTO)));
    }

    public DeviceMqttTopicsDTO updateDeviceMqttTopics(Long id, DeviceMqttTopicsDTO deviceMqttTopicsDTO) {
        DeviceMqttTopics deviceMqttTopics = deviceMqttTopicsRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("DeviceMqttTopics not found"));
        deviceMqttTopicsMapper.updateDeviceMqttTopicsFromDto(deviceMqttTopicsDTO, deviceMqttTopics);
        DeviceMqttTopics updatedDeviceMqttTopics = deviceMqttTopicsRepository.save(deviceMqttTopics);
        return deviceMqttTopicsMapper.toDto(updatedDeviceMqttTopics);
    }

    public void deleteDeviceMqttTopics(Long id) {
        DeviceMqttTopics deviceMqttTopics = deviceMqttTopicsRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "DeviceMqttTopics not found"));
        if (deviceMqttTopics.getSubscriptionStatus() != SubscriptionStatus.INACTIVE) {
            deviceMqttTopics.setSubscriptionStatus(SubscriptionStatus.INACTIVE);
            deviceMqttTopicsRepository.save(deviceMqttTopics);
        }
    }

    public DeviceMqttTopicsDTO getDeviceMqttTopics(Long id) {
        DeviceMqttTopics deviceMqttTopics = deviceMqttTopicsRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("DeviceMqttTopics not found"));
        return deviceMqttTopicsMapper.toDto(deviceMqttTopics);
    }

    public List<DeviceMqttTopicsDTO> getAllDeviceMqttTopics() {
        return deviceMqttTopicsRepository.findAll()
                .stream()
                .filter(deviceMqttTopics -> deviceMqttTopics.getSubscriptionStatus() == SubscriptionStatus.ACTIVE)
                .map(deviceMqttTopicsMapper::toDto)
                .collect(Collectors.toList());
    }
}
