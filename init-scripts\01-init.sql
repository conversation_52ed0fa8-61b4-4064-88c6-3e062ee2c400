-- VG Pay Database Initialization Script
-- This script will be executed when the MySQL container starts for the first time

-- Create database if it doesn't exist (though it's already created by environment variables)
CREATE DATABASE IF NOT EXISTS vgpaydb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Use the database
USE vgpaydb;

-- Grant privileges to the vgpay user (if created)
-- GRANT ALL PRIVILEGES ON vgpaydb.* TO 'vgpay'@'%';
-- FLUSH PRIVILEGES;

-- The application will create tables automatically using Hibernate DDL auto-update
-- You can add any initial data or additional setup here if needed

-- Example: Insert initial configuration data (uncomment if needed)
-- INSERT INTO some_config_table (key, value) VALUES ('initial_setup', 'completed');

SELECT 'VG Pay Database initialized successfully' AS message;
