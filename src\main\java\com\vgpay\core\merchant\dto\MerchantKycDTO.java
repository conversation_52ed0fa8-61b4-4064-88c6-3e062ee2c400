package com.vgpay.core.merchant.dto;

import com.vgpay.core.merchant.model.KycStatus;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.Instant;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class MerchantKycDTO {
    private Long id;              // null when creating
    private Long merchantId;      // required
    private Instant kycDueDate;
    private Instant kycDate;
    private KycStatus kycStatus;  // default PENDING if null on create
    private Instant createdAt;
    private Instant updatedAt;
}
