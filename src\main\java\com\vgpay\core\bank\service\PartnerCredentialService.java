package com.vgpay.core.bank.service;

import com.vgpay.core.bank.dto.BankPartnerDTO;
import com.vgpay.core.bank.dto.PartnerCredentialDTO;
import com.vgpay.core.bank.mapper.PartnerCredentialMapper;
import com.vgpay.core.bank.model.CredentialStatus;
import com.vgpay.core.bank.model.PartnerCredential;
import com.vgpay.core.bank.model.PartnerStatus;
import com.vgpay.core.bank.repository.PartnerCredentialRepository;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class PartnerCredentialService {

    private final PartnerCredentialRepository partnerCredentialRepository;
    private final PartnerCredentialMapper partnerCredentialMapper;

    public PartnerCredentialService(PartnerCredentialRepository partnerCredentialRepository, PartnerCredentialMapper partnerCredentialMapper) {
        this.partnerCredentialRepository = partnerCredentialRepository;
        this.partnerCredentialMapper = partnerCredentialMapper;
    }

    public PartnerCredentialDTO createPartnerCredential(PartnerCredentialDTO dto) {
        PartnerCredential partnerCredential = partnerCredentialMapper.toEntity(dto);
        partnerCredential = partnerCredentialRepository.save(partnerCredential);
        return partnerCredentialMapper.toDto(partnerCredential);
    }

    public PartnerCredentialDTO updatePartnerCredential(Long id, PartnerCredentialDTO dto) {
        PartnerCredential partnerCredential = partnerCredentialRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "PartnerCredential not found"));
        partnerCredentialMapper.updatePartnerCredentialFromDto(dto, partnerCredential);
        partnerCredential = partnerCredentialRepository.save(partnerCredential);
        return partnerCredentialMapper.toDto(partnerCredential);
    }

    public PartnerCredentialDTO getPartnerCredential(Long id) {
        PartnerCredential partnerCredential = partnerCredentialRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "PartnerCredential not found"));
        return partnerCredentialMapper.toDto(partnerCredential);
    }

    public void deletePartnerCredential(Long id) {
        PartnerCredential partnerCredential = partnerCredentialRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "PartnerCredential not found"));
        if (partnerCredential.getStatus() != CredentialStatus.INACTIVE){
            partnerCredential.setStatus(CredentialStatus.INACTIVE);
            partnerCredentialRepository.save(partnerCredential);
        }
    }

    public List<PartnerCredentialDTO> getAllActivePartnerCredentials() {
        return partnerCredentialRepository.findAll().stream()
                .filter(pc -> pc.getStatus() == CredentialStatus.ACTIVE)
                .map(partnerCredentialMapper::toDto)
                .collect(Collectors.toList());
    }


}
