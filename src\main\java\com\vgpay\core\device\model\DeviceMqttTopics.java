package com.vgpay.core.device.model;

import jakarta.persistence.*;
import lombok.*;

import java.time.Instant;

@Entity
@Table(name = "device_mqtt_topics")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DeviceMqttTopics {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;  // PK

    @Column(name = "device_id", nullable = false)
    private Long deviceId;  // FK → Device.id

    @Column(name = "mqtt_topic", nullable = false)
    private String mqttTopic;  // Topic name

    @Column(name = "subscription_date", nullable = false)
    private Instant subscriptionDate;  // Date when subscribed

    @Enumerated(EnumType.STRING)
    @Column(name = "subscription_status", nullable = false)
    private SubscriptionStatus subscriptionStatus; // ACTIVE/INACTIVE/DELETED

    @Column(name = "last_modified")
    private Instant lastModified;  // Last modified timestamp
}
