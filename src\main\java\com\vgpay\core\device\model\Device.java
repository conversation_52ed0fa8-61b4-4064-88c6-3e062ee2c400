package com.vgpay.core.device.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.Instant;

@Entity
@Table(name = "device")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Device {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;   // PK

    @Column(name = "code", nullable = false, unique = true)
    private String code;   // Unique device code

    @Column(name = "serial_number", nullable = false, unique = true)
    private String serialNumber;   // Device serial number

    @Column(name = "imei", nullable = false, unique = true)
    private String imei;   // Device IMEI number

    @Column(name = "sim_number", unique = true)
    private String simNumber;   // SIM mapped to device

    @Column(name = "firmware_version")
    private String firmwareVersion;                       // Firmware version

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private DeviceStatus status = DeviceStatus.INACTIVE;   // ACTIVE/INACTIVE/ASSIGNED

    @Column(name = "last_seen_at")
    private Instant lastSeenAt;                            // Heartbeat/last status timestamp

    @Column(name = "created_at")
    private Instant createdAt;

    @Column(name = "updated_at")
    private Instant updatedAt;


}
