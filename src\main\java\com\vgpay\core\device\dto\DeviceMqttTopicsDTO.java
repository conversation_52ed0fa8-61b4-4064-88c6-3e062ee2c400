package com.vgpay.core.device.dto;

import com.vgpay.core.device.model.SubscriptionStatus;
import lombok.*;

import java.time.Instant;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DeviceMqttTopicsDTO {

    private Long id;
    private Long deviceId;
    private String mqttTopic;
    private Instant subscriptionDate;
    private SubscriptionStatus subscriptionStatus;
    private Instant lastModified;

}

