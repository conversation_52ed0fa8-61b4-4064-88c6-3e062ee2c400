package com.vgpay.core.merchant.model;

import jakarta.persistence.*;
import lombok.*;

import java.time.Instant;

@Entity
@Table(name = "merchant_kyc")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class MerchantKYC {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;                                 // PK

    @Column(name = "merchant_id", nullable = false)
    private Long merchantId;                         // FK -> Merchant.id

    @Column(name = "kyc_due_date")
    private Instant kycDueDate;                      // From Bank

    @Column(name = "kyc_date")
    private Instant kycDate;                         // From Bank

    @Enumerated(EnumType.STRING)
    @Column(name = "kyc_status", nullable = false)
    private KycStatus kycStatus = KycStatus.PENDING; // PENDING/APPROVED/REJECTED

    @Column(name = "created_at")
    private Instant createdAt;

    @Column(name = "updated_at")
    private Instant updatedAt;
}
