package com.vgpay.core.transaction.controller;

import com.vgpay.core.transaction.dto.TransactionDTO;
import com.vgpay.core.transaction.service.TransactionService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/transaction")
public class TransactionController {
    private final TransactionService transactionService;

    public TransactionController(TransactionService transactionService) {
        this.transactionService = transactionService;
    }

    @PostMapping("")
    public ResponseEntity<String> handleWebhook(@RequestBody TransactionDTO dto) {
        transactionService.create(dto);
        return ResponseEntity.ok("ACK"); // always acknowledge PSP
    }

    @PutMapping("/{id}")
    public ResponseEntity<TransactionDTO>  updateTransaction(@PathVariable Long id, @RequestBody TransactionDTO dto) {
        return ResponseEntity.ok(transactionService.update(id, dto));
    }

    @GetMapping("/{id}")
    public ResponseEntity<TransactionDTO> getTransaction(@PathVariable Long id) {
        return ResponseEntity.ok(transactionService.getTransactionById(id));
    }
}

