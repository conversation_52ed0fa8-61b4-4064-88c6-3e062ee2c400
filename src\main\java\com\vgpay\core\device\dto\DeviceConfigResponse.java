package com.vgpay.core.device.dto;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Response with config
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeviceConfigResponse {
    private boolean success;
    private int status;
    private String message;

    private DeviceConfigDataDTO data;
    private String timestamp;

//    private String serialNumber;
//    private String audioLanguage;
//
//    private String brokerUrl;
//    private int port;
//    private String username;
//    private String password;
//    private String topic;
//
//    private String timestamp;
}
